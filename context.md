# Project: Cargo and Utility Truck Website

## 🚀 Purpose and Vision
This website is for a company that sells cargo and utility trucks. The homepage follows a modern, high-impact layout similar to that used by companies like **BYD** and **Tesla**, focusing on fullscreen "hero banner" sections that showcase each vehicle model individually in an elegant, scrollable vertical layout.

## 🧱 Site Architecture

### Current Implementation Status

#### Completed Components:
- **Navigation Bar**: Includes mega menu for truck models and contact link
- **Homepage**: Features fullscreen hero sections for different truck models
- **Model Pages**: Basic structure for individual model pages
- **Contact Page**: Basic structure with form elements

#### Pending Tasks:
- **Footer**: Create and implement site-wide footer component
- **Contact Page Content**: Enhance the contact page with additional information, map, etc.
- **Model Pages Content**: Add detailed specifications, galleries, and features to individual model pages
- **Mobile Optimization**: Further testing and optimization for small screens

### Homepage Design
- The homepage is built as a **stack of fullscreen hero sections**.
- Each section (`HeroSection.vue`) occupies `100vh` of the viewport height.
- The user scrolls vertically to navigate through different truck models.
- Currently features four truck models with different alignments.
- White text on truck image backgrounds for maximum visibility.

### Navigation
- Fixed navigation bar with logo on the left
- Mega menu for "Modelos" section showing different truck types
- Simple contact page link
- Responsive mobile hamburger menu

### Model Pages
- Each truck model has its own dedicated page with:
  - Hero section with truck image
  - Specifications section
  - Features section
  - Call-to-action button

## ⚙️ Tech Stack

- **Framework**: Vue.js 3 (with Composition API)
- **IDE**: Cursor
- **Styling**: Tailwind CSS for utility classes + custom CSS
- **Routing**: Vue Router for page navigation
- **State Management**: Simple state handling with Vue's reactive system

## 🧩 Component Architecture

### Core Components
- `Navbar.vue`: Fixed navigation with mega menu functionality
- `HeroSection.vue`: A reusable full-screen component for each truck model
  - Props: `title`, `subtitle`, `image`, `link`, `alignment`, `theme`
  - Optional slot for custom content overlay
- `HomeView.vue`: Parent container that renders all the hero sections in order
  - Loads data from a static array of truck models

### Model Pages
Five model view components for different truck types:
- `CargaView.vue`: Cargo truck details
- `CisternaView.vue`: Tanker truck details
- `VolqueteView.vue`: Dump truck details
- `TractorView.vue`: Tractor trailer details
- `FrigorificoView.vue`: Refrigerated truck details

### Current Data Structure

```js
const heroSlides = [
  {
    title: "Camión de Carga 7T",
    subtitle: "La eficiencia se une a la potencia en nuestro buque insignia...",
    image: cargoImage,
    link: "/modelos/carga",
    modelType: "Serie Carga",
    specs: { /* Truck specifications */ }
  },
  {
    title: "Reparto Urbano Pro",
    subtitle: "El compañero perfecto para la logística de ciudad...",
    image: utilitarioImage,
    link: "/models/urban-delivery",
    modelType: "Serie Urbana",
    specs: { /* Truck specifications */ }
  },
  {
    title: "Master para Trabajo Pesado",
    subtitle: "Conquista cualquier terreno con nuestro camión más potente...",
    image: camionetaImage,
    link: "/models/heavy-duty",
    modelType: "Serie Profesional",
    specs: { /* Truck specifications */ }
  },
  {
    title: "Tanque Grado Alimenticio",
    subtitle: "Diseñado para el transporte seguro y eficiente de líquidos y productos alimenticios...",
    image: tanqueImage,
    link: "/modelos/cisterna",
    modelType: "Serie Especializada",
    specs: { /* Truck specifications */ }
  }
];
```

## 📋 Upcoming Tasks

1. **Create Footer Component**:
   - Company information, social links, and navigation
   - Consistent styling with the rest of the site

2. **Enhance Contact Page**:
   - Add company information section
   - Implement map integration
   - Add success/error handling for form submission

3. **Complete Model Pages**:
   - Add detailed specifications tables
   - Include photo galleries
   - Add comparison features with other models
   - Implement downloadable brochures

4. **General Improvements**:
   - SEO optimization
   - Performance optimization
   - Cross-browser testing
   - Analytics integration
