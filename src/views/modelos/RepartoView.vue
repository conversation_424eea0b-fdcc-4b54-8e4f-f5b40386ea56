<template>
  <div class="model-page">
    <div class="model-hero">
      <h1>Reparto Urbano Pro</h1>
      <p>El compañero perfecto para la logística de ciudad, combinando agilidad con amplio espacio de almacenamiento</p>
    </div>
    
    <div class="model-details">
      <div class="model-image">
        <img src="https://placehold.co/600x400/333333/FFD700?text=Reparto+Urbano+Pro" alt="Reparto Urbano Pro">
      </div>
      
      <div class="model-specs">
        <h2>Especificaciones</h2>
        <ul>
          <li><strong>Capacidad de Carga:</strong> 3.500 kg</li>
          <li><strong>Potencia del Motor:</strong> 180 HP</li>
          <li><strong>Radio de Giro:</strong> 5,2 m</li>
          <li><strong>Volumen de Carga:</strong> 18 m³</li>
          <li><strong>Tipo de Combustible:</strong> <PERSON><PERSON><PERSON><PERSON> Eléctrico</li>
        </ul>
        
        <div class="model-features">
          <h3>Características</h3>
          <p>Nuestro modelo Reparto Urbano Pro está diseñado específicamente para la distribución logística en entornos urbanos congestionados. Su tamaño compacto y su maniobrabilidad lo hacen ideal para navegar por calles estrechas mientras mantiene una excelente capacidad de carga.</p>
          
          <div class="feature-grid">
            <div class="feature">
              <h4>Sistema híbrido eficiente</h4>
              <p>Motor híbrido que reduce el consumo y las emisiones, perfecto para zonas urbanas con restricciones ambientales.</p>
            </div>
            <div class="feature">
              <h4>Diseño compacto</h4>
              <p>Radio de giro optimizado para maniobrar en espacios reducidos y calles estrechas de la ciudad.</p>
            </div>
            <div class="feature">
              <h4>Espacio de carga optimizado</h4>
              <p>Máximo aprovechamiento del espacio interior con 18 m³ de volumen útil y múltiples opciones de configuración.</p>
            </div>
          </div>
        </div>
        
        <button class="cta-button">Solicitar Información</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Model specific logic can be added here
</script>

<style scoped>
.model-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.model-hero {
  text-align: center;
  margin-bottom: 3rem;
}

.model-hero h1 {
  color: #0056b3;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.model-hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.model-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.model-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.model-specs h2 {
  color: #0056b3;
  margin-bottom: 1.5rem;
}

.model-specs ul {
  padding-left: 1.5rem;
  margin-bottom: 2rem;
}

.model-specs li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.model-features {
  margin-bottom: 2rem;
}

.model-features h3 {
  color: #0056b3;
  margin-bottom: 1rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature {
  background: #f8f8f8;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.feature h4 {
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.cta-button {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .model-details {
    grid-template-columns: 1fr;
  }
  
  .model-image {
    margin-bottom: 2rem;
  }
}
</style> 