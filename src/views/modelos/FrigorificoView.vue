<template>
  <div class="model-page">
    <div class="model-hero">
      <h1>Camión Frigorífico</h1>
      <p>Solución especializada para el transporte de productos refrigerados y congelados con control preciso de temperatura</p>
    </div>
    
    <div class="model-details">
      <div class="model-image">
        <img src="@/assets/img/refrigerado.webp" alt="Camión Frigorífico">
      </div>
      
      <div class="model-specs">
        <h2>Especificaciones</h2>
        <ul>
          <li><strong>Capacidad de Carga:</strong> 12 toneladas</li>
          <li><strong>Potencia del Motor:</strong> 320 HP</li>
          <li><strong>Rango de Temperatura:</strong> -30°C a +20°C</li>
          <li><strong>Sistema de Refrigeración:</strong> Dual con backup automático</li>
          <li><strong>Aislamiento:</strong> Alta eficiencia térmica</li>
        </ul>
        
        <div class="model-features">
          <h3>Características</h3>
          <p>Nuestro camión frigorífico está diseñado para garantizar la cadena de frío en el transporte de alimentos, productos farmacéuticos y otros productos que requieren temperatura controlada.</p>
          
          <div class="feature-grid">
            <div class="feature">
              <h4>Control de temperatura preciso</h4>
              <p>Sistema digital que permite ajustar y monitorear la temperatura en tiempo real.</p>
            </div>
            <div class="feature">
              <h4>Aislamiento térmico de alta eficiencia</h4>
              <p>Mantiene la temperatura interior estable independientemente del clima exterior.</p>
            </div>
            <div class="feature">
              <h4>Múltiples zonas de temperatura</h4>
              <p>Posibilidad de configurar diferentes temperaturas en distintas secciones del camión.</p>
            </div>
          </div>
        </div>
        
        <button class="cta-button">Solicitar Información</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Model specific logic can be added here
</script>

<style scoped>
.model-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.model-hero {
  text-align: center;
  margin-bottom: 3rem;
}

.model-hero h1 {
  color: #0056b3;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.model-hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.model-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.model-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.model-specs h2 {
  color: #0056b3;
  margin-bottom: 1.5rem;
}

.model-specs ul {
  padding-left: 1.5rem;
  margin-bottom: 2rem;
}

.model-specs li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.model-features {
  margin-bottom: 2rem;
}

.model-features h3 {
  color: #0056b3;
  margin-bottom: 1rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature {
  background: #f8f8f8;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.feature h4 {
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.cta-button {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .model-details {
    grid-template-columns: 1fr;
  }
  
  .model-image {
    margin-bottom: 2rem;
  }
}
</style> 