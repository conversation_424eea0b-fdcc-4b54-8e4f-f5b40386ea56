<template>
  <div class="model-page">
    <div class="model-hero">
      <h1>Tanque Grado Alimenticio</h1>
      <p>Diseñado para el transporte seguro y eficiente de líquidos y productos alimenticios, cumpliendo con los más altos estándares sanitarios</p>
    </div>
    
    <div class="model-details">
      <div class="model-image">
        <img src="https://placehold.co/600x400/333333/FFD700?text=Tanque+Grado+Alimenticio" alt="Tanque Grado Alimenticio">
      </div>
      
      <div class="model-specs">
        <h2>Especificaciones</h2>
        <ul>
          <li><strong>Capacidad de Tanque:</strong> 15.000 litros</li>
          <li><strong>Material:</strong> Acero Inoxidable 304</li>
          <li><strong>Certificación:</strong> Grado Alimenticio</li>
          <li><strong>Compartimentos:</strong> 3 independientes</li>
          <li><strong>Sistema de Bombeo:</strong> Integrado</li>
        </ul>
        
        <div class="model-features">
          <h3>Características</h3>
          <p>Nuestro tanque de grado alimenticio está diseñado para transportar líquidos con total seguridad y eficiencia. Cumple con todas las normativas internacionales de seguridad para el transporte de líquidos y productos alimenticios.</p>
          
          <div class="feature-grid">
            <div class="feature">
              <h4>Sistema de compartimentos independientes</h4>
              <p>Tanque dividido en 3 compartimentos independientes para diferentes tipos de líquidos.</p>
            </div>
            <div class="feature">
              <h4>Certificación sanitaria</h4>
              <p>Certificaciones internacionales para el transporte seguro de productos alimenticios.</p>
            </div>
            <div class="feature">
              <h4>Sistema de bombeo integrado</h4>
              <p>Sistema de bombeo propio para facilitar la carga y descarga de líquidos con total seguridad.</p>
            </div>
          </div>
        </div>
        
        <button class="cta-button">Solicitar Información</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Model specific logic can be added here
</script>

<style scoped>
.model-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.model-hero {
  text-align: center;
  margin-bottom: 3rem;
}

.model-hero h1 {
  color: #0056b3;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.model-hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.model-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.model-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.model-specs h2 {
  color: #0056b3;
  margin-bottom: 1.5rem;
}

.model-specs ul {
  padding-left: 1.5rem;
  margin-bottom: 2rem;
}

.model-specs li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.model-features {
  margin-bottom: 2rem;
}

.model-features h3 {
  color: #0056b3;
  margin-bottom: 1rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature {
  background: #f8f8f8;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.feature h4 {
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.cta-button {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .model-details {
    grid-template-columns: 1fr;
  }
  
  .model-image {
    margin-bottom: 2rem;
  }
}
</style> 