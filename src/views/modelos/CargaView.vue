<template>
  <div class="model-page">
    <div class="model-hero">
      <h1>Camión de Carga</h1>
      <p>Nuestro modelo más versátil para todo tipo de transporte de carga.</p>
    </div>
    
    <div class="model-details">
      <div class="model-image">
        <img src="https://placehold.co/600x400/333333/FFD700?text=Camion+de+Carga" alt="Camión de Carga">
      </div>
      
      <div class="model-specs">
        <h2>Especificaciones</h2>
        <ul>
          <li><strong>Motor:</strong> Diesel 300HP</li>
          <li><strong>Carga Máxima:</strong> 15 toneladas</li>
          <li><strong>Combustible:</strong> 18L/100km</li>
          <li><strong>Transmisión:</strong> Manual de 6 velocidades</li>
          <li><strong>Capacidad de remolque:</strong> 25 toneladas</li>
        </ul>
        
        <div class="model-features">
          <h3>Características</h3>
          <p>Este camión de carga ofrece una combinación perfecta de potencia, eficiencia y versatilidad. Ideal para el transporte de mercancías a larga distancia o distribución urbana.</p>
          
          <div class="feature-grid">
            <div class="feature">
              <h4>Cabina Ergonómica</h4>
              <p>Diseñada para maximizar la comodidad del conductor durante viajes largos.</p>
            </div>
            <div class="feature">
              <h4>Sistema de frenos avanzado</h4>
              <p>Mayor seguridad en todo tipo de terreno y condiciones climáticas.</p>
            </div>
            <div class="feature">
              <h4>Plataforma versátil</h4>
              <p>Compatible con diversos tipos de cajas y accesorios para diferentes necesidades.</p>
            </div>
          </div>
        </div>
        
        <button class="cta-button">Solicitar Información</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Model specific logic can be added here
</script>

<style scoped>
.model-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.model-hero {
  text-align: center;
  margin-bottom: 3rem;
}

.model-hero h1 {
  color: #0056b3;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.model-hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.model-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.model-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.model-specs h2 {
  color: #0056b3;
  margin-bottom: 1.5rem;
}

.model-specs ul {
  padding-left: 1.5rem;
  margin-bottom: 2rem;
}

.model-specs li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.model-features {
  margin-bottom: 2rem;
}

.model-features h3 {
  color: #0056b3;
  margin-bottom: 1rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature {
  background: #f8f8f8;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.feature h4 {
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.cta-button {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .model-details {
    grid-template-columns: 1fr;
  }
  
  .model-image {
    margin-bottom: 2rem;
  }
}
</style> 