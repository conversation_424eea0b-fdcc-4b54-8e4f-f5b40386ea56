<template>
  <div class="model-page">
    <div class="model-hero">
      <h1>Master para Trabajo Pesado</h1>
      <p>Conquista cualquier terreno con nuestro camión más potente y confiable para trabajos pesados</p>
    </div>
    
    <div class="model-details">
      <div class="model-image">
        <img src="https://placehold.co/600x400/333333/FFD700?text=Master+Trabajo+Pesado" alt="Master para Trabajo Pesado">
      </div>
      
      <div class="model-specs">
        <h2>Especificaciones</h2>
        <ul>
          <li><strong>Capacidad de Carga:</strong> 12.000 kg</li>
          <li><strong>Potencia del Motor:</strong> 450 HP</li>
          <li><strong>Torque:</strong> 2.200 Nm</li>
          <li><strong>Tanque de Combustible:</strong> 400 L</li>
          <li><strong>Transmisión:</strong> Automática de 12 velocidades</li>
        </ul>
        
        <div class="model-features">
          <h3>Características</h3>
          <p>El Master para Trabajo Pesado está diseñado para afrontar los desafíos más exigentes en cualquier tipo de terreno y condición. Su potente motor y su robusta construcción lo convierten en la opción ideal para trabajos que requieren fuerza y durabilidad.</p>
          
          <div class="feature-grid">
            <div class="feature">
              <h4>Tracción 4x4 avanzada</h4>
              <p>Sistema de tracción a las cuatro ruedas con bloqueo de diferencial para superar los terrenos más difíciles.</p>
            </div>
            <div class="feature">
              <h4>Estructura reforzada</h4>
              <p>Chasis y carrocería diseñados para soportar cargas extremas y condiciones ambientales severas.</p>
            </div>
            <div class="feature">
              <h4>Motor de alto rendimiento</h4>
              <p>450 HP con un impresionante torque de 2.200 Nm para garantizar potencia incluso en pendientes pronunciadas.</p>
            </div>
          </div>
        </div>
        
        <button class="cta-button">Solicitar Información</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Model specific logic can be added here
</script>

<style scoped>
.model-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.model-hero {
  text-align: center;
  margin-bottom: 3rem;
}

.model-hero h1 {
  color: #0056b3;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.model-hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.model-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.model-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.model-specs h2 {
  color: #0056b3;
  margin-bottom: 1.5rem;
}

.model-specs ul {
  padding-left: 1.5rem;
  margin-bottom: 2rem;
}

.model-specs li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.model-features {
  margin-bottom: 2rem;
}

.model-features h3 {
  color: #0056b3;
  margin-bottom: 1rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature {
  background: #f8f8f8;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.feature h4 {
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.cta-button {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cta-button:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .model-details {
    grid-template-columns: 1fr;
  }
  
  .model-image {
    margin-bottom: 2rem;
  }
}
</style> 