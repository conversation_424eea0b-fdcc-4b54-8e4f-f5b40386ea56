<template>
  <div class="contacto-page">
    <div class="contacto-hero">
      <div class="hero-overlay"></div>
      <div class="hero-content">
        <h1 class="hero-title">Contáct<PERSON>s</h1>
        <p class="hero-subtitle">Estamos aquí para responder a todas sus preguntas y ayudarle con sus necesidades de transporte</p>
      </div>
    </div>
    <div class="contact-content">
      <h2>Contacto</h2>
      <div class="contact-container">
        <div class="contact-info">
          <h3>Información de Contacto</h3>
          <address>
            <p><strong>Dirección:</strong> Av. Principal 1234, Ciudad</p>
            <p><strong>Teléfono:</strong> +34 123 456 789</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Horario:</strong> Lun-Vie: 9:00 - 18:00</p>
          </address>
          
          <div class="contact-whatsapp">
            <h3>Contáctanos por WhatsApp</h3>
            <a href="https://wa.me/34123456789" class="whatsapp-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
              </svg>
              <span>Chatear ahora</span>
            </a>
            <p class="whatsapp-info">Respuesta inmediata de Lunes a Viernes 9:00 - 18:00</p>
          </div>
          
          <div class="social-links">
            <h3>Síguenos</h3>
            <div class="social-icons">
              <a href="#" aria-label="Facebook" class="social-link">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
              </a>
              <a href="#" aria-label="Twitter" class="social-link">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
              </a>
              <a href="#" aria-label="Instagram" class="social-link">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
              </a>
              <a href="#" aria-label="LinkedIn" class="social-link">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>
              </a>
            </div>
          </div>
        </div>
        
        <div class="contact-form">
          <h3>Envíenos un mensaje</h3>
          <form @submit.prevent="submitForm">
            <div class="form-group">
              <label for="nombre">Nombre</label>
              <input type="text" id="nombre" v-model="formData.nombre" required>
            </div>
            
            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" v-model="formData.email" required>
            </div>
            
            <div class="form-group">
              <label for="telefono">Teléfono</label>
              <input type="tel" id="telefono" v-model="formData.telefono">
            </div>
            
            <div class="form-group">
              <label for="mensaje">Mensaje</label>
              <textarea id="mensaje" v-model="formData.mensaje" rows="5" required></textarea>
            </div>
            
            <button type="submit" class="submit-btn">Enviar Mensaje</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

const formData = reactive({
  nombre: '',
  email: '',
  telefono: '',
  mensaje: ''
})

const submitForm = () => {
  // Form submission logic would go here
  console.log('Form submitted:', formData)
  // Reset form after submission
  formData.nombre = ''
  formData.email = ''
  formData.telefono = ''
  formData.mensaje = ''
  alert('Mensaje enviado correctamente')
}
</script>

<style scoped>
.contacto-page {
  width: 100%;
  margin: 0 auto;
}

.contacto-hero {
  position: relative;
  height: 60vh;
  background-image: url('https://placehold.co/1920x1080/0056b3/FFFFFF?text=Contacto+CLW');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: 2rem;
}

.contact-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 1rem;
  position: relative;
  z-index: 10;
}

h2 {
  margin-bottom: 2rem;
  text-align: center;
  color: #0056b3;
  font-size: 2rem;
}

h3 {
  color: #0056b3;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.contact-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

address {
  font-style: normal;
  line-height: 1.8;
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

address p {
  margin-bottom: 1rem;
}

.contact-whatsapp {
  background-color: #25D366;
  padding: 2rem;
  border-radius: 8px;
  color: white;
  box-shadow: 0 4px 16px rgba(37, 211, 102, 0.2);
}

.contact-whatsapp h3 {
  color: white;
  margin-bottom: 1.5rem;
}

.whatsapp-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: white;
  color: #075E54;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  margin-bottom: 1rem;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: fit-content;
}

.whatsapp-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.whatsapp-info {
  font-size: 0.9rem;
  opacity: 0.9;
}

.social-links {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: #f5f5f5;
  border-radius: 50%;
  color: #0056b3;
  transition: all 0.3s;
}

.social-link:hover {
  background-color: #0056b3;
  color: white;
  transform: translateY(-3px);
}

.contact-form {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input, textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

input:focus, textarea:focus {
  outline: none;
  border-color: #0056b3;
}

.submit-btn {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  width: 100%;
}

.submit-btn:hover {
  background-color: #003d82;
}

@media (max-width: 900px) {
  .contact-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .contacto-hero {
    height: 50vh;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }
}
</style> 