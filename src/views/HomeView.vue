<template>
  <main class="relative">
   

    <!-- Hero Sections -->
    <div class="relative hero-sections-container" ref="sectionsContainer">
      <HeroSection
        v-for="(slide, idx) in heroSlides"
        :key="slide.title"
        v-bind="slide"
        :alignment="getAlignment(idx)"
        :isLastSection="idx === heroSlides.length - 1"
      />
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import HeroSection from '../components/HeroSection.vue'
import cargoImage from '../assets/img/clwCargoTruck.webp'
import utilitarioImage from '../assets/img/utilitario.webp'
import camionetaImage from '../assets/img/camionetaCLW.webp'
import tanqueImage from '../assets/tanque.webp'
import refrigeradoImage from '../assets/img/refrigerado.webp'

const sectionsContainer = ref(null)
const currentSection = ref(0)

const heroSlides = [
  {
    title: "Camión de Carga 7T",
    subtitle: "La eficiencia se une a la potencia en nuestro buque insignia, diseñado para máxima carga y economía de combustible óptima",
    image: cargoImage,
    link: "/modelos/carga",
    modelType: "Serie Carga",
    specs: {
      "Capacidad de Carga": "7.000 kg",
      "Potencia del Motor": "280 HP",
      "Eficiencia de Combustible": "8,5 L/100km",
      "Altura de Carga": "1,2 m",
      "Espacio de Carga": "32 m³"
    }
  },
  {
    title: "Reparto Urbano Pro",
    subtitle: "El compañero perfecto para la logística de ciudad, combinando agilidad con amplio espacio de almacenamiento",
    image: utilitarioImage,
    link: "/modelos/reparto",
    modelType: "Serie Urbana",
    specs: {
      "Capacidad de Carga": "3.500 kg",
      "Potencia del Motor": "180 HP",
      "Radio de Giro": "5,2 m",
      "Volumen de Carga": "18 m³",
      "Tipo de Combustible": "Híbrido Eléctrico"
    }
  },
  {
    title: "Master para Trabajo Pesado",
    subtitle: "Conquista cualquier terreno con nuestro camión más potente y confiable para trabajos pesados",
    image: camionetaImage,
    link: "/modelos/master",
    modelType: "Serie Profesional",
    specs: {
      "Capacidad de Carga": "12.000 kg",
      "Potencia del Motor": "450 HP",
      "Torque": "2.200 Nm",
      "Tanque de Combustible": "400 L",
      "Transmisión": "Automática de 12 velocidades"
    }
  },
  {
    title: "Tanque Grado Alimenticio",
    subtitle: "Diseñado para el transporte seguro y eficiente de líquidos y productos alimenticios, cumpliendo con los más altos estándares sanitarios",
    image: tanqueImage,
    link: "/modelos/tanque",
    modelType: "Serie Especializada",
    specs: {
      "Capacidad de Tanque": "15.000 litros",
      "Material": "Acero Inoxidable 304",
      "Certificación": "Grado Alimenticio",
      "Compartimentos": "3 independientes",
      "Sistema de Bombeo": "Integrado"
    }
  },
  {
    title: "Camión Frigorífico",
    subtitle: "Solución especializada para el transporte de productos refrigerados y congelados con control preciso de temperatura",
    image: refrigeradoImage,
    link: "/modelos/frigorifico",
    modelType: "Serie Refrigerada",
    specs: {
      "Capacidad de Carga": "12 toneladas",
      "Potencia del Motor": "320 HP",
      "Rango de Temperatura": "-30°C a +20°C",
      "Sistema de Refrigeración": "Dual con backup",
      "Aislamiento": "Alta eficiencia térmica"
    }
  }
]

// Get alignment based on slide index
const getAlignment = (index) => {
  // First and third sections centered, second section left-aligned, fourth right-aligned, fifth centered
  const alignments = ['center', 'left', 'center', 'right', 'center'];
  return alignments[index % alignments.length];
}

// Scroll handling
const scrollToSection = (index) => {
  const sections = sectionsContainer.value.children
  sections[index].scrollIntoView({ behavior: 'smooth' })
}

const handleScroll = () => {
  const sections = sectionsContainer.value.children
  const scrollPosition = window.scrollY
  const windowHeight = window.innerHeight

  for (let i = 0; i < sections.length; i++) {
    const section = sections[i]
    const sectionTop = section.offsetTop
    const sectionBottom = sectionTop + section.offsetHeight

    if (scrollPosition >= sectionTop - windowHeight / 3 && 
        scrollPosition < sectionBottom - windowHeight / 3) {
      currentSection.value = i
      break
    }
  }
}

// Lifecycle hooks
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  handleScroll() // Initial check
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* Smooth scrolling for the entire page */
:root {
  scroll-behavior: smooth;
}

/* Hide scrollbar but keep functionality */
::-webkit-scrollbar {
  display: none;
}

/* Negative margin to compensate for the main padding */
.hero-sections-container {
  margin-top: -80px; /* Offset the main padding-top */
}
</style> 