<template>
  <section
    class="relative w-full h-screen flex items-center justify-center bg-cover bg-center bg-fixed overflow-hidden"
    :style="{ backgroundImage: `url(${image})` }"
    :class="{ 'last-section': isLastSection }"
  >
    <!-- Gradient overlay for better text readability -->
    <div
      class="absolute inset-0 bg-gradient-to-b from-black/10 via-transparent to-black/30 z-0"
      :class="theme === 'dark' ? 'opacity-100' : 'opacity-50'"
    ></div>

    <!-- New centered content overlay -->
    <div class="absolute top-[15%] left-1/2 transform -translate-x-1/2 text-center max-w-2xl px-4 z-10">
      <h1 class="hero-title text-[3rem] md:text-[5rem] font-bold font-title text-white mb-6 leading-none tracking-tight" style="color: white !important;">{{ title }}</h1>
      <p class="mt-3 text-xl md:text-2xl text-white font-body max-w-xl mx-auto">{{ subtitle }}</p>
      <div class="mt-10 flex justify-center">
        <router-link 
          :to="link" 
          style="background-color: rgba(255, 255, 255, 0.4);"
          class="spec-button rounded-full backdrop-blur-sm text-gray-600 font-semibold p-[15px] border border-gray-300 shadow-lg hover:shadow-gray-400/20 transition-all duration-300 ease-out transform hover:scale-105 hover:translate-y-[-2px] active:scale-95 text-lg flex items-center gap-3 relative overflow-hidden group"
        >
          <span class="relative z-10 mx-1 px-2">Especificaciones</span>
          <svg class="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
          </svg>
          <div class="absolute inset-0 bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </router-link>
      </div>
    </div>

    <!-- Optional slot for custom content -->
    <slot />
  </section>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  image: String,
  link: String,
  modelType: {
    type: String,
    default: 'Nuevo Modelo'
  },
  alignment: {
    type: String,
    default: 'center',
  },
  theme: {
    type: String,
    default: 'light',
  },
  showScrollIndicator: {
    type: Boolean,
    default: false
  },
  isLastSection: {
    type: Boolean,
    default: false
  }
})

const alignmentClass = computed(() => {
  const baseClasses = 'flex flex-col max-w-6xl'
  
  switch (props.alignment) {
    case 'left':
      return `${baseClasses} items-start text-left ml-0 mr-auto`;
    case 'right':
      return `${baseClasses} items-end text-right ml-auto mr-0`;
    case 'center':
    default:
      return `${baseClasses} items-center text-center mx-auto`;
  }
})
</script>

<style scoped>
.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Smooth scroll behavior for the page */
:root {
  scroll-behavior: smooth;
}

/* Last section should have padding to accommodate footer */
.last-section {
  margin-bottom: 0;
  min-height: calc(100vh - 200px); /* Adjusted to make room for footer */
}

/* Override default browser link active color */
.spec-button,
.spec-button:active,
.spec-button:visited,
.spec-button:any-link {
  color: #666666 !important;
  -webkit-text-fill-color: #666666 !important;
}

/* Fix for Safari/WebKit */
.spec-button:active {
  color: #666666 !important;
  -webkit-text-fill-color: #666666 !important;
  -webkit-tap-highlight-color: transparent;
}

/* Override WebKit default h1 styling */
.hero-title {
  font-size: 3rem !important;
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
  color: white !important;
  -webkit-text-fill-color: white !important;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 5rem !important;
  }
}

:-webkit-any(article, aside, nav, section) h1.hero-title {
  font-size: 3rem !important; 
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
  color: white !important;
}

@media (min-width: 768px) {
  :-webkit-any(article, aside, nav, section) h1.hero-title {
    font-size: 5rem !important;
  }
}

/* Estilo explícito para el botón */
.spec-button {
  background-color: rgba(255, 255, 255, 0.4) !important;
}
</style> 