<template>
  <header class="global-header">
    <div class="header-content">
      <div class="logo">
        <router-link to="/">
          <img src="@/assets/img/CLWAzul.svg" alt="CLW Logo">
        </router-link>
      </div>
      <button 
        class="burger" 
        @click="toggleNav" 
        aria-label="Toggle navigation menu" 
        :aria-expanded="navOpen"
        aria-controls="main-nav"
      >
        <span :class="{ open: navOpen }"></span>
        <span :class="{ open: navOpen }"></span>
        <span :class="{ open: navOpen }"></span>
      </button>
      <nav 
        class="main-nav" 
        :class="{ open: navOpen }" 
        id="main-nav"
        role="navigation"
      >
        <ul>
          <li 
            class="has-mega" 
            @mouseenter="handleMegaInteraction('modelos', true)"
            @mouseleave="handleMegaInteraction('modelos', false)"
            @focusin="handleMegaInteraction('modelos', true)"
            @focusout="handleMegaInteraction('modelos', false)"
          >
            <button 
              @click.stop="toggleMega('modelos')"
              class="mega-trigger"
              :aria-expanded="megaOpen === 'modelos'"
              aria-controls="modelos-menu"
            >
              Modelos
              <span class="sr-only">Toggle truck models menu</span>
            </button>
            <div 
              id="modelos-menu"
              class="mega-menu mega-menu-grid" 
              :class="{ active: megaOpen === 'modelos' }"
              role="menu"
              aria-label="Truck models menu"
              @click.stop
            >
              <div class="mega-grid">
                <router-link 
                  v-for="model in truckModels" 
                  :key="model.title" 
                  :to="model.link" 
                  class="mega-tile"
                  role="menuitem"
                  tabindex="0"
                  @click="closeNav"
                >
                  <img :src="model.img" :alt="model.title" />
                  <span>{{ model.title }}</span>
                </router-link>
              </div>
            </div>
          </li>
          <li><router-link to="/contacto" @click="closeNav">Contacto</router-link></li>
        </ul>
      </nav>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const navOpen = ref(false)
const megaOpen = ref(null)
const isDesktop = ref(window.innerWidth > 900)
let megaTimeout = null

const truckModels = [
  { title: 'Camión de Carga 7T', link: '/modelos/carga', img: '/assets/img/menu/carga.webp' },
  { title: 'Reparto Urbano Pro', link: '/modelos/reparto', img: '/assets/img/menu/reparto.webp' },
  { title: 'Master para Trabajo Pesado', link: '/modelos/master', img: '/assets/img/menu/trabajo.webp' },
  { title: 'Tanque Grado Alimenticio', link: '/modelos/tanque', img: '/assets/img/menu/alimenticio.webp' },
  { title: 'Camión Frigorífico', link: '/modelos/frigorifico', img: '/assets/img/menu/frigorifico.webp' },
]

function handleResize() {
  isDesktop.value = window.innerWidth > 900
  if (!isDesktop.value) {
    megaOpen.value = null
  }
}

function handleMegaInteraction(menu, isEntering) {
  if (!isDesktop.value) return
  
  if (megaTimeout) {
    clearTimeout(megaTimeout)
    megaTimeout = null
  }

  if (isEntering) {
    megaOpen.value = menu
  } else {
    megaTimeout = setTimeout(() => {
      if (megaOpen.value === menu) {
        megaOpen.value = null
      }
    }, 150)
  }
}

function toggleNav() {
  navOpen.value = !navOpen.value
  if (!navOpen.value) {
    megaOpen.value = null
  }
}

function closeNav() {
  if (!isDesktop.value) {
    navOpen.value = false
    megaOpen.value = null
  }
}

function toggleMega(menu) {
  if (!isDesktop.value) {
    megaOpen.value = megaOpen.value === menu ? null : menu
  }
}

// Handle keyboard navigation
function handleKeyDown(event) {
  if (event.key === 'Escape') {
    closeNav()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
:root {
  --primary-blue: #0056b3;
  --secondary-blue: #003366;
  --accent-gray: #e0e0e0;
  --light-gray: #f5f5f5;
  --text-dark: #212121;
  --text-white: #ffffff;
  --transition: all 0.3s ease;
}

.global-header {
  background-color: white;
  color: var(--text-dark);
  padding: 1rem 2rem;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 45px;
  transition: var(--transition);
}

.main-nav {
  position: static;
}

.main-nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.main-nav ul li.has-mega {
  position: static;
}

.main-nav ul li a {
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
  position: relative;
}

.main-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-blue);
  transition: var(--transition);
  transform: translateX(-50%);
}

.main-nav ul li a:hover::after {
  width: 100%;
}

.main-nav ul li a:hover {
  color: var(--primary-blue);
}

/* Burger menu */
.burger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1200;
}

.burger span {
  display: block;
  width: 28px;
  height: 3px;
  margin: 3px 0;
  background: var(--primary-blue);
  border-radius: 2px;
  transition: 0.3s;
}

.burger span.open:nth-child(1) {
  transform: translateY(6px) rotate(45deg);
}

.burger span.open:nth-child(2) {
  opacity: 0;
}

.burger span.open:nth-child(3) {
  transform: translateY(-6px) rotate(-45deg);
}

/* Mega menu styles */
.mega-trigger {
  background: none;
  border: none;
  color: var(--text-dark);
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mega-trigger:hover,
.mega-trigger:focus {
  color: var(--primary-blue);
  outline: none;
}

.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  transform: translateY(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 999;
}

.mega-menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.mega-menu.mega-menu-grid {
  min-width: 100%;
  padding: 2rem 0;
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.mega-grid {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: stretch;
  gap: 2rem;
}

.mega-tile {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--text-dark);
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-align: center;
}

.mega-tile:hover {
  background: var(--light-gray);
}

.mega-tile:hover img {
  transform: scale(1.05);
}

.mega-tile:hover span {
  color: var(--primary-blue);
}

.mega-tile img {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.mega-tile span {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-dark);
  transition: color 0.3s ease;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive styles */
@media (max-width: 900px) {
  .burger {
    display: flex;
  }
  
  .main-nav {
    position: fixed;
    top: 76px;
    left: 0;
    width: 100%;
    height: 0;
    background: white;
    overflow: hidden;
    transition: height 0.3s ease;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .main-nav.open {
    height: calc(100vh - 76px);
    overflow-y: auto;
  }
  
  .main-nav ul {
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    height: auto;
  }
  
  .main-nav ul li a {
    font-size: 1.2rem;
    padding: 0.8rem 0;
    width: 100%;
    display: block;
    text-align: center;
  }
  
  .mega-menu {
    position: static;
    max-height: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    overflow: hidden;
    margin: 0;
    padding: 0;
  }
  
  .mega-menu.active {
    max-height: 1000px;
    opacity: 1;
    visibility: visible;
    margin-top: 1rem;
  }
  
  .mega-menu.mega-menu-grid {
    width: 100%;
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
  }
  
  .mega-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0;
  }
  
  .mega-tile {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    text-align: left;
    flex-direction: row;
  }
  
  .mega-tile img {
    width: 80px;
    height: 60px;
    margin: 0;
  }
  
  .mega-trigger {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
  }
  
  .mega-trigger::after {
    content: '';
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--text-dark);
    border-bottom: 2px solid var(--text-dark);
    transform: rotate(45deg);
    transition: transform 0.3s ease;
  }
  
  .mega-trigger[aria-expanded="true"]::after {
    transform: rotate(-135deg);
  }
  
  .main-nav ul li.has-mega {
    width: 100%;
  }
}
</style> 