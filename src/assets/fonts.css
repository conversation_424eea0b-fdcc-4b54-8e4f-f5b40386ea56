/* Google Fonts Import */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@400;500&family=DM+Sans:wght@400;500&display=swap');

/* Font Families */

:root {
    /* Title font */
    --font-title: 'Poppins', sans-serif;
    /* Body and UI font */
    --font-body: 'Inter', 'DM Sans', system-ui, sans-serif;
}


/* Typography Classes */

.font-title {
    font-family: var(--font-title);
    font-weight: 600;
}

.font-body {
    font-family: var(--font-body);
    font-weight: 400;
}

.font-body-medium {
    font-family: var(--font-body);
    font-weight: 500;
}


/* Special text treatments */

.watermark {
    font-family: var(--font-title);
    font-weight: 400;
    opacity: 0.1;
}


/* Badge formatting */

.badge {
    font-family: var(--font-body);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}


/* Button text */

.btn-text {
    font-family: var(--font-body);
    font-weight: 500;
    letter-spacing: 0.02em;
}