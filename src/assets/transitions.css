/* Fade and slide transitions */

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease, transform 0.5s ease-out;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(20px);
}


/* Slide transitions */

.slide-enter-active,
.slide-leave-active {
    transition: transform 0.5s ease-in-out;
}

.slide-enter-from {
    transform: translateX(100%);
}

.slide-leave-to {
    transform: translateX(-100%);
}


/* Scale transitions */

.scale-enter-active,
.scale-leave-active {
    transition: all 0.5s ease;
}

.scale-enter-from,
.scale-leave-to {
    opacity: 0;
    transform: scale(0.9);
}


/* Custom animations */

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}


/* Smooth scroll behavior */

html {
    scroll-behavior: smooth;
}


/* Hide scrollbar for Chrome, Safari and Opera */

.no-scrollbar::-webkit-scrollbar {
    display: none;
}


/* Hide scrollbar for IE, Edge and Firefox */

.no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
}


/* Backdrop blur utilities */

.backdrop-blur-xs {
    backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

.backdrop-blur-md {
    backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
    backdrop-filter: blur(12px);
}