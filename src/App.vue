<template>
  <div id="app">
    <Navbar />
    <main>
      <router-view />
    </main>
    <Footer />
  </div>
</template>

<script setup>
import Navbar from './components/Navbar.vue';
import Footer from './components/Footer.vue';
// App shell for routing
</script>

<style>
/* Base styles */
body {
  font-family: 'Inter', 'DM Sans', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

html, body {
  overflow-x: hidden;
}

h1, h2, h3 {
  font-family: 'Poppins', sans-serif;
  letter-spacing: -0.02em;
  font-weight: 600;
}

button, a {
  font-family: 'Inter', 'DM Sans', system-ui, sans-serif;
}

main {
  padding-top: 80px; /* Space for fixed navbar */
  min-height: calc(100vh - 80px);
}
</style>
