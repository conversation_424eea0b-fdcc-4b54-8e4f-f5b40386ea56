import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const routes = [{
        path: '/',
        name: 'Home',
        component: HomeView
    },
    {
        path: '/contacto',
        name: '<PERSON>o',
        component: () =>
            import ('../views/ContactoView.vue')
    },
    // Truck model routes
    {
        path: '/modelos/carga',
        name: 'CamionCarga',
        component: () =>
            import ('../views/modelos/CargaView.vue')
    },
    {
        path: '/modelos/tanque',
        name: 'TanqueAlimenticio',
        component: () =>
            import ('../views/modelos/TanqueView.vue')
    },
    // Redirect from old path to new path
    {
        path: '/modelos/cisterna',
        redirect: '/modelos/tanque'
    },
    {
        path: '/modelos/reparto',
        name: 'RepartoUrbanoPro',
        component: () =>
            import ('../views/modelos/RepartoView.vue')
    },
    // Redirect from old path to new path
    {
        path: '/modelos/volquete',
        redirect: '/modelos/reparto'
    },
    {
        path: '/modelos/master',
        name: 'MasterTrabajoPesado',
        component: () =>
            import ('../views/modelos/MasterView.vue')
    },
    // Redirect from old path to new path
    {
        path: '/modelos/tractor',
        redirect: '/modelos/master'
    },
    {
        path: '/modelos/frigorifico',
        name: 'CamionFrigorifico',
        component: () =>
            import ('../views/modelos/FrigorificoView.vue')
    },
    // Legal pages
    {
        path: '/privacidad',
        name: 'PoliticaPrivacidad',
        component: () =>
            import ('../views/PrivacyPolicyView.vue')
    },
    {
        path: '/terminos',
        name: 'TerminosUso',
        component: () =>
            import ('../views/TerminosView.vue')
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        // Always scroll to top when changing routes
        return { top: 0 }
    }
})

export default router