<template>
  <div id="app">
    <header class="global-header">
      <div class="header-content">
        <div class="logo">
          <img src="@/assets/img/logo.webp" alt="Global Link Logo">
          <h1>Global Link</h1>
        </div>
        <button 
          class="burger" 
          @click="toggleNav" 
          aria-label="Toggle navigation menu" 
          :aria-expanded="navOpen"
          aria-controls="main-nav"
        >
          <span :class="{ open: navOpen }"></span>
          <span :class="{ open: navOpen }"></span>
          <span :class="{ open: navOpen }"></span>
        </button>
        <nav 
          class="main-nav" 
          :class="{ open: navOpen }" 
          id="main-nav"
          role="navigation"
        >
          <ul>
            <li><router-link to="/" @click="closeNav">Inicio</router-link></li>
            <li 
              class="has-mega" 
              @mouseenter="handleMegaInteraction('sectores', true)"
              @mouseleave="handleMegaInteraction('sectores', false)"
              @focusin="handleMegaInteraction('sectores', true)"
              @focusout="handleMegaInteraction('sectores', false)"
            >
              <button 
                @click.stop="toggleMega('sectores')"
                class="mega-trigger"
                :aria-expanded="megaOpen === 'sectores'"
                aria-controls="sectores-menu"
              >
                Sectores
                <span class="sr-only">Toggle sectors menu</span>
              </button>
              <div 
                id="sectores-menu"
                class="mega-menu mega-menu-grid" 
                :class="{ active: megaOpen === 'sectores' }"
                role="menu"
                aria-label="Sectors menu"
                @click.stop
              >
                <div class="mega-grid">
                  <router-link 
                    v-for="sector in sectores" 
                    :key="sector.title" 
                    :to="sector.link" 
                    class="mega-tile"
                    role="menuitem"
                    tabindex="0"
                    @click="closeNav"
                  >
                    <img :src="sector.img" :alt="sector.title" />
                    <span>{{ sector.title }}</span>
                  </router-link>
                </div>
              </div>
            </li>
            <li 
              class="has-mega"
              @mouseenter="handleMegaInteraction('servicios', true)"
              @mouseleave="handleMegaInteraction('servicios', false)"
              @focusin="handleMegaInteraction('servicios', true)"
              @focusout="handleMegaInteraction('servicios', false)"
            >
              <button 
                @click.stop="toggleMega('servicios')"
                class="mega-trigger"
                :aria-expanded="megaOpen === 'servicios'"
                aria-controls="servicios-menu"
              >
                Servicios
                <span class="sr-only">Toggle services menu</span>
              </button>
              <div 
                id="servicios-menu"
                class="mega-menu mega-menu-grid" 
                :class="{ active: megaOpen === 'servicios' }"
                role="menu"
                aria-label="Services menu"
                @click.stop
              >
                <div class="mega-grid">
                  <router-link 
                    v-for="servicio in servicios" 
                    :key="servicio.title" 
                    :to="servicio.link" 
                    class="mega-tile"
                    role="menuitem"
                    tabindex="0"
                    @click="closeNav"
                  >
                    <img :src="servicio.img" :alt="servicio.title" />
                    <span>{{ servicio.title }}</span>
                  </router-link>
                </div>
              </div>
            </li>
            <li><router-link to="/catalogo" class="catalog-btn" @click="closeNav">Catálogo</router-link></li>
            <li><router-link to="/contacto" @click="closeNav">Contacto</router-link></li>
          </ul>
        </nav>
      </div>
    </header>

    <main>
      <router-view />
    </main>

    <footer>
      <GlobalFooter />
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import GlobalFooter from './components/Footer.vue';
import imgIndustrial from '@/assets/img/gl_industrial.webp'
import imgComercial from '@/assets/img/gl_comercial.webp'
import imgAgricola from '@/assets/img/gl_agricola.webp'
import imgConstruccion from '@/assets/img/gl_construccion.webp'
import imgRenovables from '@/assets/img/gl_renovables.webp'

const navOpen = ref(false)
const megaOpen = ref(null)
const isDesktop = ref(window.innerWidth > 900)
let megaTimeout = null

const sectores = [
  { title: 'Agrícolas', link: '/agricolas', img: imgAgricola },
  { title: 'Comerciales', link: '/comerciales', img: imgComercial },
  { title: 'Construcción', link: '/construccion', img: imgConstruccion },
  { title: 'Industriales', link: '/industriales', img: imgIndustrial },
  { title: 'Energías Renovables', link: '/renovables', img: imgRenovables },
]

const servicios = [
  { title: 'Importación', link: '/importacion', img: 'https://placehold.co/120x80/333333/FFD700?text=Importacion' },
  { title: 'Exportación', link: '/exportacion', img: 'https://placehold.co/120x80/333333/FFD700?text=Exportacion' },
  { title: 'Logística', link: '/logistica', img: 'https://placehold.co/120x80/333333/FFD700?text=Logistica' },
]

function handleResize() {
  isDesktop.value = window.innerWidth > 900
  if (!isDesktop.value) {
    megaOpen.value = null
  }
}

function handleMegaInteraction(menu, isEntering) {
  if (!isDesktop.value) return
  
  if (megaTimeout) {
    clearTimeout(megaTimeout)
    megaTimeout = null
  }

  if (isEntering) {
    megaOpen.value = menu
  } else {
    megaTimeout = setTimeout(() => {
      if (megaOpen.value === menu) {
        megaOpen.value = null
      }
    }, 150)
  }
}

function toggleNav() {
  navOpen.value = !navOpen.value
  if (!navOpen.value) {
    megaOpen.value = null
  }
}

function closeNav() {
  if (!isDesktop.value) {
    navOpen.value = false
    megaOpen.value = null
  }
}

function toggleMega(menu) {
  if (!isDesktop.value) {
    megaOpen.value = megaOpen.value === menu ? null : menu
  }
}

// Handle keyboard navigation
function handleKeyDown(event) {
  if (event.key === 'Escape') {
    closeNav()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style>
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

:root {
  --primary-black: #1a1a1a;
  --secondary-gray: #333333;
  --accent-yellow: #FFD700;
  --light-gray: #f5f5f5;
  --text-white: #ffffff;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}

body {
  background-color: var(--light-gray);
  color: var(--primary-black);
}

.global-header {
  background-color: var(--primary-black);
  color: var(--text-white);
  padding: 1.2rem 2rem;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo img {
  height: 45px;
  transition: var(--transition);
}

.logo img:hover {
  transform: scale(1.05);
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.main-nav {
  position: static;
}

.main-nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.main-nav ul li.has-mega {
  position: static;
}

.main-nav ul li.has-mega > span {
  color: var(--text-white);
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
}

.main-nav ul li.has-mega > span:hover {
  color: var(--accent-yellow);
}

.main-nav ul li a {
  color: var(--text-white);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
  position: relative;
}

.main-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--accent-yellow);
  transition: var(--transition);
  transform: translateX(-50%);
}

.main-nav ul li a:hover::after {
  width: 100%;
}

.main-nav ul li a:hover {
  color: var(--accent-yellow);
}

.catalog-btn {
  background-color: var(--accent-yellow);
  color: var(--primary-black) !important;
  padding: 0.8rem 1.5rem !important;
  border-radius: 4px;
  font-weight: 600 !important;
  transition: var(--transition);
}

.catalog-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

.catalog-btn::after {
  display: none;
}

main {
  margin-top: 80px;
  min-height: calc(100vh - 80px - 300px);
  padding: 2rem;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

/* Add burger menu styles */
.burger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1200;
}

@media (max-width: 1024px) {
  .main-nav ul {
    gap: 1rem;
  }
  
  .main-nav ul li a {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}

@media (max-width: 900px) {
  .header-content {
    /* flex-direction: column; */
    gap: 1rem;
  }
  .logo {
    flex: 1;
  }
  .burger {
    display: flex;
    z-index: 1200;
    margin-left: auto;
  }
  .main-nav {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--primary-black);
    overflow: hidden;
    transition: height 0.3s ease;
  }
  .main-nav.open {
    height: calc(100vh - 80px);
    overflow-y: auto;
  }
  .main-nav ul {
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    height: auto;
  }
  .main-nav ul li a {
    font-size: 1.2rem;
    padding: 0.8rem 0;
    width: 100%;
    display: block;
    text-align: center;
    background: transparent;
  }
  .main-nav ul li a:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  .main-nav ul li.has-mega .mega-menu {
    position: relative;
    top: 0;
    transform: none;
    height: 0;
    opacity: 1;
    visibility: visible;
    transition: height 0.3s ease;
  }
  .main-nav ul li.has-mega .mega-menu.active {
    height: auto;
    margin: 1rem 0;
  }
  .main-nav ul li.has-mega .mega-menu.mega-menu-grid {
    width: 100%;
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
  }
  .main-nav ul li.has-mega .mega-grid {
    padding: 0;
    gap: 1rem;
  }
  .main-nav ul li.has-mega .mega-tile {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }
  .main-nav ul li.has-mega .mega-tile img {
    width: 80px;
    height: 60px;
    margin: 0;
  }
  .main-nav ul li.has-mega .mega-tile span {
    align-self: center;
  }
  .main-nav ul li.has-mega .mega-trigger {
    width: 100%;
    justify-content: center;
    padding: 1rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
  }
}

.burger span {
  display: block;
  width: 28px;
  height: 4px;
  margin: 4px 0;
  background: var(--accent-yellow);
  border-radius: 2px;
  transition: 0.3s;
}
.burger span.open:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}
.burger span.open:nth-child(2) {
  opacity: 0;
}
.burger span.open:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

.mega-trigger {
  background: none;
  border: none;
  color: var(--text-white);
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mega-trigger:hover,
.mega-trigger:focus {
  color: var(--accent-yellow);
  outline: none;
}

.mega-menu {
  position: fixed;
  top: 80px;
  left: 0;
  width: 100%;
  transform: translateY(-20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 999;
}

.mega-menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.mega-menu.mega-menu-grid {
  min-width: 100%;
  padding: 2rem 0;
  background: var(--primary-black);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
}

.mega-grid {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: stretch;
  gap: 2rem;
}

.mega-tile {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-white);
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  background: transparent;
}

.mega-tile:hover img {
  transform: scale(1.05);
}

.mega-tile:hover span {
  color: var(--accent-yellow);
}

.mega-tile img {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 1.5rem;
  background: #222;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.mega-tile span {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-white);
  transition: color 0.3s ease;
  padding-right: 1rem;
  line-height: 1.3;
  display: flex;
  align-items: center;
  min-height: 44px; /* Ensures consistent height even with 2 lines */
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media (max-width: 900px) {
  .main-nav {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--primary-black);
    overflow: hidden;
    transition: height 0.3s ease;
  }

  .main-nav.open {
    height: calc(100vh - 80px);
    overflow-y: auto;
    padding: 1rem;
  }

  .main-nav ul {
    flex-direction: column;
    gap: 1rem;
    height: auto;
    padding: 0;
  }

  .mega-menu {
    position: static;
    max-height: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    overflow: hidden;
    margin: 0;
    padding: 0;
  }

  .mega-menu.active {
    max-height: 1000px;
    opacity: 1;
    visibility: visible;
    margin-top: 1rem;
  }

  .mega-menu.mega-menu-grid {
    width: 100%;
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
  }

  .mega-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .mega-tile {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin: 0;
  }

  .mega-tile:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mega-trigger {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: background-color 0.3s ease;
  }

  .mega-trigger:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .mega-trigger::after {
    content: '';
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--text-white);
    border-bottom: 2px solid var(--text-white);
    transform: rotate(45deg);
    transition: transform 0.3s ease;
  }

  .mega-trigger[aria-expanded="true"]::after {
    transform: rotate(-135deg);
  }

  .main-nav ul li.has-mega {
    width: 100%;
  }
}

/* Update main nav styles for better mega menu support */
.main-nav {
  position: static;  /* Changed to static to support full-width mega menu */
}

.main-nav ul li.has-mega {
  position: static;  /* Changed to static to support full-width mega menu */
}

.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  transform: translateY(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.mega-menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

@media (max-width: 900px) {
  .main-nav {
    position: fixed;  /* Keep fixed position for mobile */
  }
  
  .mega-menu {
    position: static;
    transform: none;
    opacity: 0;
    max-height: 0;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .mega-menu.active {
    opacity: 1;
    max-height: 1000px;
    transform: none;
  }
}

/* Ensure proper stacking context */
.header-content {
  z-index: 1000;
}

.mega-menu {
  z-index: 999;
}
</style>